const _ = require('lodash');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const redisConnection = require('../../../../connections/redis');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = async (req, res) => {
  try {
    const { username, password } = req.body;

    // Check params
    if (!username || !password) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Find user
    const user = await UserModel.findOne({
      username,
      status: 1
    }).lean();

    if (!user) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_EXISTS
      });
    }

    // Check if user is OAuth user without password
    if (!user.password && user.provider !== 'local') {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Please use OAuth login for this account'
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);

    if (!isMatch) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.INCORRECT_PASSWORD
      });
    }

    // Create new token (simplified - skip Redis for now)
    const token = jwt.sign({ username, id: user._id }, config.secretKey);

    // Store in Redis (with error handling)
    const userId = user._id.toString();
    const objSign = {
      id: userId,
      role: user.role,
    };

    try {
      const redis = redisConnection('master').getConnection();

      // Use callback style but don't await - fire and forget for now
      redis.set(`user:${userId}`, token, (err) => {
        if (err) console.log('Redis set user token error:', err);
      });

      redis.set(`user:${token}`, JSON.stringify(objSign), (err) => {
        if (err) console.log('Redis set token data error:', err);
      });
    } catch (redisError) {
      console.error('Redis connection error:', redisError);
      // Continue anyway - token will still work for this session
    }

    // Send response
    const responseData = _.merge({}, user, { token });
    _.unset(responseData, 'password');

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: responseData,
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
  }
};
