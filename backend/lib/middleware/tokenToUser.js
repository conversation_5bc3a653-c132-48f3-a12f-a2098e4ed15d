const _ = require('lodash')
const jwt = require('jsonwebtoken')
const redisConnections = require('../connections/redis')
const CONSTANTS = require('../const');
const MESSAGES = require('../message');

module.exports = (req, res, next) => {
    let token = _.get(req, 'headers.token', '');

    if (!token) {
        return res.json({
            code: CONSTANTS.CODE.TOKEN_EXPIRE,
            message: MESSAGES.USER.TOKEN_EXPIRE
        });
    }

    // First try to verify JWT token
    try {
        const decoded = jwt.verify(token, config.secretKey);

        // Set user info from JWT
        req.user = {
            id: decoded.id,
            username: decoded.username
        };

        // Try to get additional info from Redis (non-blocking)
        try {
            const redis = redisConnections('master').getConnection();
            redis.get(`user:${token}`, (err, result) => {
                if (!err && result) {
                    try {
                        const objSign = JSON.parse(result);
                        if (objSign.id) {
                            req.user = objSign;
                        }
                    } catch (parseError) {
                        console.log('Redis parse error (non-critical):', parseError);
                    }
                }
            });
        } catch (redisError) {
            console.log('Redis error (non-critical):', redisError);
        }

        next();
    } catch (jwtError) {
        console.log('JWT verification failed:', jwtError.message);
        return res.json({
            code: CONSTANTS.CODE.TOKEN_EXPIRE,
            message: MESSAGES.USER.TOKEN_EXPIRE
        });
    }
}
