const express = require('express');
const cors = require('cors');
const path = require('path');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyTokenMiddleware = require('./lib/middleware/verifyToken');

// Handle routes
const ApiRoutes = require('./lib/routes/api');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').createServer(app);
global.io = require('socket.io')(server);

// Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));

// Disable caching for static files - DISABLED (frontend container serves React app)
// app.use(express.static('public', {
//   setHeaders: (res, path) => {
//     res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
//     res.setHeader('Pragma', 'no-cache');
//     res.setHeader('Expires', '0');
//   }
// }));

// Force serve the correct React app - DISABLED (frontend container serves React app)
// app.get('/', (req, res) => {
//   res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
//   res.setHeader('Pragma', 'no-cache');
//   res.setHeader('Expires', '0');
//   res.sendFile(path.join(__dirname, 'public', 'index.html'));
// });

// Test route to serve React app
app.get('/app', (req, res) => {
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.sendFile(path.join(__dirname, 'public', 'react-app.html'));
});

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  next();
});

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// Import upload middleware
const { uploadSingle, handleUploadError } = require('./lib/middleware/upload');

// API Routes - Example routes for the template
try {
  console.log('Registering routes...');
  declareRoute('post', '/auth/login', [], ApiRoutes.auth.login);
  declareRoute('post', '/auth/register', [], ApiRoutes.auth.register);
  declareRoute('post', '/user/profile', [tokenToUserMiddleware], ApiRoutes.user.profile);
  declareRoute('post', '/user/update', [tokenToUserMiddleware], ApiRoutes.user.update);

  // File management routes
  declareRoute('post', '/files/upload', [uploadSingle, handleUploadError], ApiRoutes.files.upload);
  declareRoute('get', '/files/download/:fileId', [], ApiRoutes.files.download);
  declareRoute('get', '/files/preview/:fileId', [], ApiRoutes.files.preview);
  console.log('About to register browse route...');
  declareRoute('get', '/browse/:folderId?', [], ApiRoutes.browse);
  console.log('Browse route registered successfully');

  // Folder routes
  declareRoute('post', '/folders/create', [], ApiRoutes.folders.create);
  declareRoute('post', '/folders', [], ApiRoutes.folders.create); // Alias for frontend compatibility
  console.log('Folder routes registered successfully');
  console.log('All routes registered successfully');
} catch (error) {
  console.error('Error registering routes:', error);
}

declareRoute('put', '/items/:id/rename', [], ApiRoutes.items.rename);
declareRoute('delete', '/items/:id', [], ApiRoutes.items.delete);
declareRoute('get', '/search', [], ApiRoutes.search);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

const port = _.get(config, 'port', 3000);
console.log('About to start server on port:', port);
console.log('Express app routes:');
app._router.stack.forEach((middleware, index) => {
  if (middleware.route) {
    console.log(`${index}: ${middleware.route.stack[0].method.toUpperCase()} ${middleware.route.path}`);
  } else if (middleware.name === 'router') {
    console.log(`${index}: Router middleware`);
  } else {
    console.log(`${index}: ${middleware.name} middleware`);
  }
});

server.listen(port, '0.0.0.0', () => {
  logger.logInfo('Server listening at port:', port);
  console.log('Server started successfully!');
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
